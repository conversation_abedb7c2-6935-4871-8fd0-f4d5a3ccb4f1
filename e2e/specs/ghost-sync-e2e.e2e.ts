import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Wait for async operations to complete with smart polling
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Helper to access plugin sync metadata via Obsidian
 */
async function getSyncMetadata(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(({ path }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (!plugin || !plugin.syncMetadata) {
      throw new Error('Ghost sync plugin or syncMetadata not found');
    }

    // Get the TFile object - try both with and without leading slash
    let file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file && !path.startsWith('/')) {
      file = (window as any).app.vault.getAbstractFileByPath('/' + path);
    }
    if (!file && path.startsWith('/')) {
      file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
    }

    if (!file) {
      const allFiles = (window as any).app.vault.getAllLoadedFiles()
        .filter((f: any) => f.path.endsWith('.md'))
        .map((f: any) => f.path);
      throw new Error(`File not found: ${path}. Available files: ${allFiles.join(', ')}`);
    }

    return plugin.syncMetadata.getMetadata(file);
  }, { path: filePath });
}

/**
 * Helper to create a test file with specific content using Obsidian's vault API
 */
async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path, fileContent }) => {
    try {
      // Create the file using Obsidian's vault API
      await (window as any).app.vault.create(path, fileContent);
      return true;
    } catch (error) {
      // If file already exists, modify it instead
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        await (window as any).app.vault.modify(file, fileContent);
        return true;
      }
      throw error;
    }
  }, { path: filePath, fileContent: content });
}

/**
 * Helper to get file content from Obsidian
 */
async function getFileContent(page: Page, filePath: string): Promise<string> {
  return await page.evaluate(async ({ path }) => {
    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }
    return await (window as any).app.vault.read(file);
  }, { path: filePath });
}

/**
 * Helper to trigger sync operations using the plugin's sync functionality
 */
async function performSyncToGhost(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(async ({ path }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (!plugin) {
      throw new Error('Ghost sync plugin not found');
    }

    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }

    // Use the plugin's existing sync functionality
    // First, open the file to make it the current file
    await (window as any).app.workspace.getLeaf().openFile(file);

    // Wait a bit for the file to be opened
    await new Promise(resolve => setTimeout(resolve, 500));

    // Access the sync status view and trigger sync
    try {
      await plugin.activateSyncStatusView();

      // Wait for the view to be ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get the sync status view
      const leaves = (window as any).app.workspace.getLeavesOfType('ghost-sync-status');
      if (leaves.length === 0) {
        throw new Error('Sync status view not found');
      }

      const syncView = leaves[0].view;
      if (!syncView) {
        throw new Error('Sync view not accessible');
      }

      // Trigger sync to Ghost using the view's performSync method
      // We'll simulate the sync choice by directly calling the sync service
      const ghostAPI = new plugin.ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);

      // Parse the file content manually
      const content = await (window as any).app.vault.read(file);
      const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---/);

      if (!frontMatterMatch) {
        throw new Error('No frontmatter found in file');
      }

      // Parse frontmatter
      const frontMatterText = frontMatterMatch[1];
      const frontMatter = {};
      frontMatterText.split('\n').forEach(line => {
        const match = line.match(/^([^:]+):\s*(.*)$/);
        if (match) {
          const key = match[1].trim();
          let value = match[2].trim();
          if (value.startsWith('"') && value.endsWith('"')) {
            value = value.slice(1, -1);
          }
          frontMatter[key] = value;
        }
      });

      const markdownContent = content.replace(/^---\n[\s\S]*?\n---\n/, '');

      const slug = frontMatter.slug || frontMatter.Slug;
      if (!slug) {
        throw new Error('No slug found in frontmatter');
      }

      // Check if post exists in Ghost
      let existingPost = null;
      try {
        existingPost = await ghostAPI.getPostBySlug(slug);
      } catch (error) {
        if (!error.message?.includes('404')) {
          throw error;
        }
      }

      // Create post data for Ghost
      const postData = {
        title: frontMatter.title || frontMatter.Title,
        slug: slug,
        html: markdownContent, // Ghost will convert this
        status: frontMatter.status || frontMatter.Status || 'draft',
        featured: frontMatter.featured === 'true' || frontMatter.Featured === 'true',
        visibility: frontMatter.visibility || frontMatter.Visibility || 'public'
      };

      // Add ID if updating existing post
      if (existingPost) {
        postData.id = existingPost.id;
      }

      // Sync to Ghost
      let result;
      if (existingPost) {
        result = await ghostAPI.updatePost(postData);
      } else {
        result = await ghostAPI.createPost(postData);
      }

      // Update sync metadata
      await plugin.syncMetadata.setSyncedAt(file, result.updated_at);

      return {
        success: true,
        ghostPost: result,
        localPost: { frontMatter, content: markdownContent }
      };

    } catch (error) {
      console.error('Sync error:', error);
      throw error;
    }
  }, { path: filePath });
}

describe("Ghost Sync - Real API E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to be ready
    await waitForAsyncOperation(1000);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('test-post') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }

    // Wait for file system operations to complete
    await waitForAsyncOperation(500);
  });

  test("should create local post and sync to Ghost successfully", async () => {
    const testTitle = "Test Post";
    const testSlug = "test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a local test file with the test-post slug
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Featured Image: null
Newsletter: null
---

# ${testTitle}

This is test content for e2e sync testing.

## Test Section

Some test content to verify sync works correctly.`;

    await createTestFile(page, relativeFilePath, content);
    console.log(`Created local test file: ${relativeFilePath}`);

    // Verify file was created
    const fileExists = await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      return !!file;
    }, relativeFilePath);

    expect(fileExists).toBe(true);

    // Perform sync to Ghost
    console.log("Syncing to Ghost...");
    const syncResult = await performSyncToGhost(page, relativeFilePath);

    expect(syncResult.success).toBe(true);
    expect(syncResult.ghostPost).toBeTruthy();
    expect(syncResult.ghostPost.title).toBe(testTitle);
    expect(syncResult.ghostPost.slug).toBe(testSlug);

    console.log(`✅ Successfully synced to Ghost: ${syncResult.ghostPost.title}`);

    // Verify sync metadata was updated
    await waitForAsyncOperation(1000);
    const metadata = await getSyncMetadata(page, relativeFilePath);

    expect(metadata.synced_at).toBeTruthy();
    expect(new Date(metadata.synced_at).getTime()).toBeGreaterThan(0);

    console.log(`✅ Sync metadata updated: synced_at = ${metadata.synced_at}`);
  });

  test("should detect sync status correctly", async () => {
    const testTitle = "Test Post";
    const testSlug = "test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create local file
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

Test content for sync status detection.`;

    await createTestFile(page, relativeFilePath, content);

    // Analyze sync status using Ghost API
    const analysis = await page.evaluate(async ({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);

      if (!plugin || !file) {
        throw new Error('Plugin or file not found');
      }

      // Use the plugin's Ghost API
      const ghostAPI = new plugin.ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);

      // Parse local file
      const content = await (window as any).app.vault.read(file);
      const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---/);

      if (!frontMatterMatch) {
        throw new Error('No frontmatter found in file');
      }

      // Parse frontmatter
      const frontMatterText = frontMatterMatch[1];
      const frontMatter = {};
      frontMatterText.split('\n').forEach(line => {
        const match = line.match(/^([^:]+):\s*(.*)$/);
        if (match) {
          const key = match[1].trim();
          let value = match[2].trim();
          if (value.startsWith('"') && value.endsWith('"')) {
            value = value.slice(1, -1);
          }
          frontMatter[key] = value;
        }
      });

      // Get Ghost post
      const ghostPost = await ghostAPI.getPostBySlug('test-post');

      return {
        hasGhostPost: !!ghostPost,
        localTitle: frontMatter.title || frontMatter.Title,
        ghostTitle: ghostPost?.title,
        localSlug: frontMatter.slug || frontMatter.Slug,
        ghostSlug: ghostPost?.slug
      };
    }, { path: relativeFilePath });

    expect(analysis.hasGhostPost).toBe(true);
    expect(analysis.localTitle).toBe(testTitle);
    expect(analysis.ghostTitle).toBeTruthy();
    expect(analysis.localSlug).toBe(testSlug);
    expect(analysis.ghostSlug).toBe(testSlug);

    console.log(`✅ Sync analysis completed: Ghost post exists = ${analysis.hasGhostPost}`);
    console.log(`Local title: ${analysis.localTitle}, Ghost title: ${analysis.ghostTitle}`);
    console.log(`Local slug: ${analysis.localSlug}, Ghost slug: ${analysis.ghostSlug}`);
  });

  test("should sync from Ghost to local successfully", async () => {
    const testTitle = "Test Post";
    const testSlug = "test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a minimal local file first
    const initialContent = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

Initial local content.`;

    await createTestFile(page, relativeFilePath, initialContent);

    // Sync from Ghost to local
    console.log("Syncing from Ghost to local...");
    const syncResult = await page.evaluate(async ({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);

      if (!plugin || !file) {
        throw new Error('Plugin or file not found');
      }

      // Use the plugin's Ghost API
      const ghostAPI = new plugin.ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);

      // Get Ghost post
      const ghostPost = await ghostAPI.getPostBySlug('test-post');
      if (!ghostPost) {
        throw new Error('Ghost post not found');
      }

      // Convert Ghost post to article content
      const frontMatter = {
        Title: ghostPost.title,
        Slug: ghostPost.slug,
        Status: ghostPost.status,
        'Featured Image': ghostPost.feature_image || 'null',
        Newsletter: 'null'
      };

      // Create frontmatter string
      const frontMatterString = Object.entries(frontMatter)
        .map(([key, value]) => `${key}: "${value}"`)
        .join('\n');

      // Create article content
      const articleContent = `---
${frontMatterString}
---

${ghostPost.html || ghostPost.plaintext || ''}`;

      // Update the local file
      await (window as any).app.vault.modify(file, articleContent);

      // Update sync metadata
      await plugin.syncMetadata.setSyncFromGhost(file, ghostPost.updated_at);

      return {
        success: true,
        ghostPost: ghostPost
      };
    }, { path: relativeFilePath });

    expect(syncResult.success).toBe(true);
    expect(syncResult.ghostPost).toBeTruthy();

    // Verify the local file was updated with Ghost content
    await waitForAsyncOperation(1000);
    const updatedContent = await getFileContent(page, relativeFilePath);

    // The content should now contain Ghost post content
    expect(updatedContent).toContain(testTitle);
    expect(updatedContent).toContain(testSlug);

    console.log(`✅ Successfully synced from Ghost to local`);

    // Verify sync metadata was updated
    const metadata = await getSyncMetadata(page, relativeFilePath);
    expect(metadata.synced_at).toBeTruthy();
    expect(metadata.changed_at).toBeTruthy();

    console.log(`✅ Sync metadata updated after Ghost sync`);
  });

  test("should handle sync conflicts correctly", async () => {
    const testTitle = "Test Post";
    const testSlug = "test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create local file with different content than Ghost
    const localContent = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

This is LOCAL content that differs from Ghost.

## Local Section

Local changes that should be detected.`;

    await createTestFile(page, relativeFilePath, localContent);

    // Mark the file as changed to simulate local modifications
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Analyze sync status to detect conflicts
    const conflictAnalysis = await page.evaluate(async ({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);

      if (!plugin || !file) {
        throw new Error('Plugin or file not found');
      }

      // Use the plugin's Ghost API
      const ghostAPI = new plugin.ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);

      // Get Ghost post
      const ghostPost = await ghostAPI.getPostBySlug('test-post');

      return {
        hasLocalChanges: !!plugin.syncMetadata.getChangedAt(file),
        hasGhostPost: !!ghostPost,
        localChangedAt: plugin.syncMetadata.getChangedAt(file),
        localSyncedAt: plugin.syncMetadata.getSyncedAt(file),
        ghostUpdatedAt: ghostPost?.updated_at
      };
    }, { path: relativeFilePath });

    expect(conflictAnalysis.hasLocalChanges).toBe(true);
    expect(conflictAnalysis.hasGhostPost).toBe(true);
    expect(conflictAnalysis.localChangedAt).toBeTruthy();

    console.log(`✅ Conflict detection working: Local changes = ${conflictAnalysis.hasLocalChanges}`);
    console.log(`Local changed_at: ${conflictAnalysis.localChangedAt}`);
    console.log(`Local synced_at: ${conflictAnalysis.localSyncedAt}`);
  });

  test("should maintain sync metadata consistency", async () => {
    const testTitle = "Test Post";
    const testSlug = "test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create and sync a file
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
---

# ${testTitle}

Content for metadata consistency test.`;

    await createTestFile(page, relativeFilePath, content);

    // Perform initial sync to Ghost
    await performSyncToGhost(page, relativeFilePath);
    await waitForAsyncOperation(1000);

    // Get initial metadata
    const initialMetadata = await getSyncMetadata(page, relativeFilePath);
    expect(initialMetadata.synced_at).toBeTruthy();

    console.log(`Initial sync metadata: ${JSON.stringify(initialMetadata)}`);

    // Make a local change
    const updatedContent = content.replace('Content for metadata consistency test.', 'UPDATED content for metadata test.');
    await page.evaluate(async ({ path, newContent }) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        await (window as any).app.vault.modify(file, newContent);
      }
    }, { path: relativeFilePath, newContent: updatedContent });

    // Mark as changed
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Get updated metadata
    const updatedMetadata = await getSyncMetadata(page, relativeFilePath);

    expect(updatedMetadata.changed_at).toBeTruthy();
    expect(updatedMetadata.synced_at).toBe(initialMetadata.synced_at); // Should remain the same
    expect(new Date(updatedMetadata.changed_at).getTime()).toBeGreaterThan(new Date(initialMetadata.synced_at).getTime());

    console.log(`✅ Metadata consistency maintained after local changes`);
    console.log(`Updated metadata: ${JSON.stringify(updatedMetadata)}`);
  });
});
