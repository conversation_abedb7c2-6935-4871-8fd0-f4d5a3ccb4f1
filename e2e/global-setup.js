#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');

const OBSIDIAN_PATH = '/Applications/Obsidian.app/Contents/MacOS/Obsidian';
const USER_DATA_DIR = path.join(process.cwd(), 'e2e/test_obsidian_data');
const VAULT_PATH = path.join(process.cwd(), 'tests/vault/Test');
const DEBUG_PORT = 9222;
const HEALTH_CHECK_URL = `http://127.0.0.1:${DEBUG_PORT}/json`;

let obsidianProcess = null;

async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function checkObsidianReady() {
    try {
        const response = await fetch(HEALTH_CHECK_URL);
        const data = await response.json();
        return Array.isArray(data) && data.length > 0;
    } catch (error) {
        return false;
    }
}

async function waitForObsidian(maxWaitSeconds = 30) {
    console.log('⏳ Waiting for Obsidian to be ready...');

    for (let i = 0; i < maxWaitSeconds; i++) {
        if (await checkObsidianReady()) {
            console.log('✅ Obsidian is ready!');
            return true;
        }
        process.stdout.write('.');
        await sleep(1);
    }

    console.log('\n❌ Obsidian failed to start within timeout');
    return false;
}

async function waitForObsidianApp(maxWaitSeconds = 30) {
    console.log('⏳ Waiting for Obsidian app object to be ready...');

    const { chromium } = require('playwright');

    for (let i = 0; i < maxWaitSeconds; i++) {
        try {
            const browser = await chromium.connectOverCDP('http://127.0.0.1:9222');
            const contexts = browser.contexts();

            if (contexts.length > 0) {
                const context = contexts[0];
                const pages = context.pages();

                if (pages.length > 0) {
                    const page = pages[0];

                    // Check if app.plugins is available
                    const appReady = await page.evaluate(() => {
                        return typeof window.app !== 'undefined' &&
                               typeof window.app.plugins !== 'undefined';
                    });

                    await browser.close();

                    if (appReady) {
                        console.log('✅ Obsidian app object is ready!');
                        return true;
                    }
                } else {
                    await browser.close();
                }
            } else {
                await browser.close();
            }
        } catch (error) {
            // Connection failed, continue waiting
        }

        process.stdout.write('.');
        await sleep(1);
    }

    console.log('\n❌ Obsidian app object failed to initialize within timeout');
    return false;
}

function buildPlugin() {
    return new Promise((resolve, reject) => {
        console.log('🔨 Building plugin...');
        exec('npm run build', (error) => {
            if (error) {
                console.error('❌ Build failed:', error);
                reject(error);
            } else {
                console.log('✅ Plugin built successfully');
                resolve();
            }
        });
    });
}

function copyPluginFiles() {
    return new Promise((resolve, reject) => {
        console.log('📁 Copying plugin files to test vault...');
        exec('cp main.js manifest.json styles.css tests/vault/Test/.obsidian/plugins/ghost-sync/', (error) => {
            if (error) {
                console.error('❌ Copy failed:', error);
                reject(error);
            } else {
                console.log('✅ Plugin files copied');
                resolve();
            }
        });
    });
}

function startObsidian() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Starting Obsidian...');

        obsidianProcess = spawn(OBSIDIAN_PATH, [
            `--user-data-dir=${USER_DATA_DIR}`,
            `--remote-debugging-port=${DEBUG_PORT}`,
            VAULT_PATH
        ], {
            stdio: 'pipe',
            detached: false
        });

        obsidianProcess.stdout.on('data', (data) => {
            const output = data.toString();
            if (output.includes('DevTools listening')) {
                console.log('✅ Obsidian started with DevTools');
                resolve();
            }
        });

        obsidianProcess.stderr.on('data', () => {
            // Ignore stderr for now as Obsidian outputs some warnings
        });

        obsidianProcess.on('error', (error) => {
            console.error('❌ Failed to start Obsidian:', error);
            reject(error);
        });

        obsidianProcess.on('exit', (code) => {
            if (code !== 0) {
                console.log(`⚠️  Obsidian exited with code ${code}`);
            }
        });

        // Fallback: resolve after 3 seconds even if we don't see the DevTools message
        setTimeout(() => {
            console.log('⏳ Obsidian should be starting...');
            resolve();
        }, 3000);
    });
}

function cleanup() {
    if (obsidianProcess) {
        console.log('🧹 Cleaning up Obsidian process...');
        obsidianProcess.kill('SIGTERM');

        // Force kill after 5 seconds if it doesn't exit gracefully
        setTimeout(() => {
            if (obsidianProcess && !obsidianProcess.killed) {
                console.log('🔪 Force killing Obsidian process...');
                obsidianProcess.kill('SIGKILL');
            }
        }, 5000);
    }
}

// Global setup function called by Vitest
export async function setup() {
    try {
        console.log('🚀 Starting E2E test environment...');

        // Setup
        await buildPlugin();
        await copyPluginFiles();

        // Check if Obsidian is already running
        const alreadyRunning = await checkObsidianReady();

        if (alreadyRunning) {
            console.log('🔍 Obsidian is already running, skipping startup...');
        } else {
            // Start Obsidian only if it's not already running
            await startObsidian();

            // Wait for Obsidian to be ready
            const isReady = await waitForObsidian();
            if (!isReady) {
                throw new Error('Obsidian failed to start');
            }
        }

        // Wait for app object to be ready (whether we started it or it was already running)
        const appReady = await waitForObsidianApp();
        if (!appReady) {
            throw new Error('Obsidian app object failed to initialize');
        }

        console.log('🎉 E2E test environment ready!');

    } catch (error) {
        console.error('💥 E2E setup failed:', error.message);
        // Only cleanup if we started the process
        if (obsidianProcess) {
            cleanup();
        }
        throw error;
    }
}

// Global teardown function called by Vitest
export async function teardown() {
    console.log('🧹 Tearing down E2E test environment...');
    cleanup();
    // Give some time for cleanup
    await sleep(2);
    console.log('✅ E2E teardown complete');
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n⚠️  Received SIGINT, cleaning up...');
    cleanup();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n⚠️  Received SIGTERM, cleaning up...');
    cleanup();
    process.exit(0);
});
